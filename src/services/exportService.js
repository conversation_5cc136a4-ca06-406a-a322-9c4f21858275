/**
 * Export Service - Handles document export functionality
 * Supports PDF, DOCX, and HTML formats
 */

import { hasExistingChapterHeading } from "../utils/contentProcessing.js";
import imageOverlayService from "./imageOverlayService.js";
import { incrementTemplateUsage } from "./templateService.js";
import coverPreviewService from "./coverPreviewService.js";
import { hashObject } from "../utils/changeDetection.js";

import { prodLogger } from "../utils/prodLogger.js";
/**
 * Retrieve text overlay customizations from local storage
 * @param {string} templateId - Template ID to get customizations for
 * @returns {Object} Customizations object or empty object if none found
 */
const getTextOverlayCustomizations = (templateId, documentData) => {
  if (!templateId) return {};

  try {
    // FIXED: Use consistent hashing method with text overlay editor
    const documentHash = documentData
      ? hashObject({ title: documentData.title, author: documentData.author })
      : "default";
    const storageKey = `text-overlay-customizations-${templateId}-${documentHash}`;

    const stored = localStorage.getItem(storageKey);
    if (stored) {
      const customizations = JSON.parse(stored);
      prodLogger.debug(
        "📝 Retrieved DOCUMENT-SPECIFIC text overlay customizations for export",
        {
          templateId,
          documentHash,
          customizationCount: Object.keys(customizations).length,
          storageKey,
          customizations, // Log actual data for debugging
          documentData: {
            title: documentData?.title,
            author: documentData?.author,
          },
        }
      );
      return customizations;
    } else {
      prodLogger.debug(
        "🔍 No document-specific text customizations found in local storage",
        {
          templateId,
          documentHash,
          storageKey,
          allStorageKeys: Object.keys(localStorage).filter((k) =>
            k.includes("text")
          ),
        }
      );
    }
  } catch (error) {
    prodLogger.warn("Failed to retrieve text overlay customizations:", error);
  }

  return {};
};

/**
 * Retrieve logo overlay customizations from local storage
 * @param {string} templateId - Template ID to get customizations for
 * @returns {Object} Logo customizations object or empty object if none found
 */
const getLogoOverlayCustomizations = (templateId, documentData) => {
  if (!templateId) return {};

  try {
    // FIXED: Use document-specific storage key to prevent cross-contamination
    const documentHash = documentData
      ? btoa(
          JSON.stringify({
            title: documentData.title,
            author: documentData.author,
          })
        ).substring(0, 8)
      : "default";
    const storageKey = `logo-overlay-customizations-${templateId}-${documentHash}`;

    const stored = localStorage.getItem(storageKey);
    if (stored) {
      const customizations = JSON.parse(stored);
      prodLogger.debug(
        "🎨 Retrieved DOCUMENT-SPECIFIC logo overlay customizations for export",
        {
          templateId,
          documentHash,
          customizationCount: Object.keys(customizations).length,
          customizations, // Add detailed logging
          storageKey,
          documentData: {
            title: documentData?.title,
            author: documentData?.author,
          },
        }
      );
      return customizations;
    } else {
      prodLogger.debug(
        "🔍 No document-specific logo customizations found in local storage",
        {
          templateId,
          documentHash,
          storageKey,
          allStorageKeys: Object.keys(localStorage).filter((k) =>
            k.includes("logo")
          ),
        }
      );
    }
  } catch (error) {
    prodLogger.warn("Failed to retrieve logo overlay customizations:", error);
  }

  return {};
};

// Utility function to clean editor HTML content for export
const cleanEditorHTMLForExport = (editorHTML) => {
  if (!editorHTML) return "";

  let cleanHTML = editorHTML;

  // Remove image suggestion cards
  cleanHTML = cleanHTML.replace(
    /<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>/gi,
    ""
  );

  // CRITICAL: Handle problematic image sources that can cause DOCX export to hang
  // BUT preserve AI-generated and uploaded images which should be exported
  cleanHTML = cleanHTML.replace(
    /<img([^>]+)src="data:[^"]*"([^>]*)>/gi,
    (fullMatch) => {
      // Check if this is an AI-generated image that should be preserved
      const isAIGenerated = /data-ai-generated\s*=\s*["']true["']/i.test(
        fullMatch
      );

      // Check if this is an uploaded image that should be preserved
      const isUploaded = /data-uploaded\s*=\s*["']true["']/i.test(fullMatch);

      if (isAIGenerated) {
        // Preserve AI-generated images for export
        prodLogger.debug("✅ Preserving AI-generated image for export");
        return fullMatch;
      } else if (isUploaded) {
        // Preserve uploaded images for export
        prodLogger.debug("✅ Preserving uploaded image for export");
        return fullMatch;
      } else {
        // Replace truly problematic base64 images with placeholder
        prodLogger.debug(
          "🚫 Replacing problematic base64 image with placeholder"
        );
        return "<p><em>[Image: Base64 data - not exported]</em></p>";
      }
    }
  );

  // Remove images with empty or invalid URLs
  cleanHTML = cleanHTML.replace(
    /<img[^>]+src=""[^>]*>/gi,
    "<p><em>[Image: No source URL provided]</em></p>"
  );

  // Remove images with obviously invalid URLs that would cause timeouts
  cleanHTML = cleanHTML.replace(
    /<img([^>]+)src="(blob:|javascript:|#)[^"]*"([^>]*)>/gi,
    "<p><em>[Image: Invalid URL format - not exported]</em></p>"
  );

  // Remove any empty paragraphs that might be left
  cleanHTML = cleanHTML.replace(/<p>\s*<\/p>/g, "");

  // Remove any wrapper divs that might interfere with export
  cleanHTML = cleanHTML.replace(
    /<div[^>]*class="[^"]*image-suggestion-card[^"]*"[^>]*>[\s\S]*?<\/div>/gi,
    ""
  );

  // FIXED: Remove excessive <br> tags that cause spacing issues
  // Remove multiple consecutive <br> tags (more than 2)
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>){3,}/gi, "<br><br>");

  // Remove <br> tags immediately after heading closing tags
  cleanHTML = cleanHTML.replace(/(<\/h[1-6]>)\s*(<br\s*\/?>)+/gi, "$1");

  // Remove <br> tags immediately before heading opening tags
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>)+\s*(<h[1-6][^>]*>)/gi, "$2");

  // Remove <br> tags at the beginning of paragraphs
  cleanHTML = cleanHTML.replace(/(<p[^>]*>)\s*(<br\s*\/?>)+/gi, "$1");

  // Remove <br> tags at the end of paragraphs (before closing </p>)
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>)+\s*(<\/p>)/gi, "$2");

  return cleanHTML.trim();
};

// FIXED: Parse editor HTML content into proper chapter structure for better PDF pagination
const parseEditorContentIntoChapters = (editorHTML) => {
  if (!editorHTML) return '<div class="chapter-content"></div>';

  // Split content by H1 and H2 headings to create chapter boundaries
  const headingRegex = /<h([12])([^>]*)>(.*?)<\/h[12]>/gi;
  const parts = [];
  let lastIndex = 0;
  let match;

  // Find all H1 and H2 headings
  while ((match = headingRegex.exec(editorHTML)) !== null) {
    // Add content before this heading (if any)
    if (match.index > lastIndex) {
      const beforeContent = editorHTML.substring(lastIndex, match.index).trim();
      if (beforeContent) {
        parts.push({
          type: "content",
          html: beforeContent,
        });
      }
    }

    // Add the heading and determine chapter level
    const headingLevel = parseInt(match[1]);
    const headingAttributes = match[2];
    const headingText = match[3];

    parts.push({
      type: "heading",
      level: headingLevel,
      attributes: headingAttributes,
      text: headingText,
      fullMatch: match[0],
    });

    lastIndex = match.index + match[0].length;
  }

  // Add any remaining content after the last heading
  if (lastIndex < editorHTML.length) {
    const remainingContent = editorHTML.substring(lastIndex).trim();
    if (remainingContent) {
      parts.push({
        type: "content",
        html: remainingContent,
      });
    }
  }

  // If no headings found, treat entire content as one chapter
  if (parts.length === 0 || parts.every((part) => part.type === "content")) {
    return `<div class="chapter">
      <div class="chapter-content">${editorHTML}</div>
    </div>`;
  }

  // Group content into chapters based on headings
  let structuredHTML = "";
  let currentChapter = "";
  let hasOpenChapter = false;

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];

    if (part.type === "heading") {
      // Close previous chapter if open
      if (hasOpenChapter) {
        currentChapter += "</div></div>";
        structuredHTML += currentChapter;
      }

      // Start new chapter
      currentChapter = `<div class="chapter">
        <h${part.level} class="chapter-title"${part.attributes}>${part.text}</h${part.level}>
        <div class="chapter-content">`;
      hasOpenChapter = true;

      // Look ahead to include content until next heading
      let j = i + 1;
      while (j < parts.length && parts[j].type === "content") {
        currentChapter += parts[j].html;
        j++;
      }

      // Skip the content parts we just processed
      i = j - 1;
    }
  }

  // Close the last chapter if open
  if (hasOpenChapter) {
    currentChapter += "</div></div>";
    structuredHTML += currentChapter;
  }

  return structuredHTML;
};

// ROBUST FALLBACK: JavaScript-based pagination fix for browsers with poor CSS support
const addJavaScriptPaginationFix = () => {
  return `
<script>
// PDF Pagination Fix: Ensure headings stay with content during print
(function() {
  'use strict';

  // Function to ensure headings have sufficient content following them
  function ensureHeadingsWithContent() {
    const chapters = document.querySelectorAll('.chapter');

    chapters.forEach((chapter, index) => {
      const title = chapter.querySelector('.chapter-title');
      const content = chapter.querySelector('.chapter-content');

      if (!title || !content) return;

      // Calculate approximate content height
      const contentText = content.textContent || '';
      const contentWords = contentText.trim().split(/\\s+/).length;
      const estimatedLines = Math.ceil(contentWords / 12); // ~12 words per line

      // If content is too short (less than 3 lines), add minimum padding
      if (estimatedLines < 3) {
        // Add invisible padding to ensure heading doesn't appear alone
        const padding = document.createElement('div');
        padding.style.minHeight = '150px';
        padding.style.visibility = 'hidden';
        padding.innerHTML = '&nbsp;'.repeat(50);
        content.appendChild(padding);
      }
    });
  }

  // Apply fixes when document is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(ensureHeadingsWithContent, 100);
    });
  } else {
    setTimeout(ensureHeadingsWithContent, 100);
  }

  // Apply fixes before print
  window.addEventListener('beforeprint', function() {
    ensureHeadingsWithContent();
  });
})();
</script>`;
};

// Utility function to convert markdown to HTML with image handling
const markdownToHtml = (markdown) => {
  if (!markdown) return "";

  return (
    markdown
      // Images - convert to HTML img tags
      .replace(
        /!\[(.*?)\]\((.*?)\)/gim,
        '<img src="$2" alt="$1" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin: 20px 0;" />'
      )
      // Headers
      .replace(/^### (.*$)/gim, "<h3>$1</h3>")
      .replace(/^## (.*$)/gim, "<h2>$1</h2>")
      .replace(/^# (.*$)/gim, "<h1>$1</h1>")
      // Bold
      .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
      // Italic
      .replace(/\*(.*)\*/gim, "<em>$1</em>")
      // Lists
      .replace(/^\* (.*$)/gim, "<li>$1</li>")
      .replace(/(<li>.*<\/li>)/gims, "<ul>$1</ul>")
      // FIXED: Remove excessive line breaks after headings and convert remaining newlines properly
      // Remove newlines immediately after closing heading tags
      .replace(/(<\/h[1-6]>)\s*\n+/gim, "$1")
      // Remove newlines immediately after closing list tags
      .replace(/(<\/ul>|<\/ol>)\s*\n+/gim, "$1")
      // Convert double newlines to paragraph breaks (preserve paragraph structure)
      .replace(/\n\s*\n/gim, "</p><p>")
      // Convert remaining single newlines to line breaks (within paragraphs)
      .replace(/\n/gim, "<br>")
      // Wrap content in paragraphs if not already wrapped
      .replace(/^(?!<[h1-6]|<ul|<ol|<img|<p)/gim, "<p>")
      .replace(/(?<!>)$/gim, "</p>")
      // Clean up any empty paragraphs or malformed paragraph tags
      .replace(/<p>\s*<\/p>/gim, "")
      .replace(/<p>(<h[1-6])/gim, "$1")
      .replace(/(<\/h[1-6]>)<\/p>/gim, "$1")
      .replace(/<p>(<ul|<ol)/gim, "$1")
      .replace(/(<\/ul>|<\/ol>)<\/p>/gim, "$1")
      .replace(/<p>(<img)/gim, "$1")
      .replace(/(<img[^>]*>)<\/p>/gim, "$1")
  );
};

// Helper function to generate template image HTML using image overlay service
const generateTemplateImageHTML = async (template, documentData) => {
  try {
    prodLogger.debug("🎨 Generating template image for export:", template.name);

    // Validate template structure
    if (!template.background_image_url || !template.text_overlays) {
      throw new Error(
        "Invalid template structure: missing background_image_url or text_overlays"
      );
    }

    // Check for text and logo overlay customizations
    const textOverlayCustomizations = getTextOverlayCustomizations(
      template.id,
      documentData
    );
    const logoOverlayCustomizations = getLogoOverlayCustomizations(
      template.id,
      documentData
    );
    const hasTextCustomizations =
      Object.keys(textOverlayCustomizations).length > 0;
    const hasLogoCustomizations =
      Object.keys(logoOverlayCustomizations).length > 0;
    const hasAnyCustomizations = hasTextCustomizations || hasLogoCustomizations;

    let canvas;
    if (hasAnyCustomizations) {
      // Merge text and logo customizations
      const mergedCustomizations = {
        ...textOverlayCustomizations,
        ...logoOverlayCustomizations,
      };

      prodLogger.debug(
        "🎨 Applying overlay customizations to legacy template image",
        {
          templateId: template.id,
          textCustomizationCount: Object.keys(textOverlayCustomizations).length,
          logoCustomizationCount: Object.keys(logoOverlayCustomizations).length,
          textCustomizations: textOverlayCustomizations,
          logoCustomizations: logoOverlayCustomizations,
          mergedCustomizations,
        }
      );

      // Render template with customizations
      canvas = await imageOverlayService.renderTemplateWithCustomizations(
        template,
        documentData,
        mergedCustomizations
      );
    } else {
      // Render template without customizations
      canvas = await imageOverlayService.renderTemplate(
        template,
        documentData,
        {},
        {}
      );
    }

    const imageDataUrl = imageOverlayService.exportAsImage(canvas, "png", 0.95); // Higher quality for export

    prodLogger.debug("✅ Template image generated successfully for export");

    // FULL-BLEED: Return HTML with full-page cover styling
    return `<div class="template-cover" style="
      page-break-after: always;
      width: 100%;
      height: 100vh;
      margin: 0;
      padding: 0;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    ">
      <img src="${imageDataUrl}" alt="Document Cover" style="
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        margin: 0;
        padding: 0;
      " />
    </div>`;
  } catch (error) {
    prodLogger.error("❌ Error generating template image for export:", error);

    // Enhanced fallback with clean white styling for Skip Template mode
    const fallbackHTML = `<div class="template-cover-fallback" style="text-align: center; margin-bottom: 40px; padding: 60px 40px; background: white; color: #333; border: 2px solid #e5e7eb; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
      <h1 class="title" style="font-size: 2.5em; margin-bottom: 20px; font-weight: bold; color: #333;">${
        documentData.title || "Untitled Document"
      }</h1>
      ${
        documentData.author
          ? `<div class="author" style="font-size: 1.3em; margin-bottom: 15px; opacity: 0.9; font-style: italic;">by ${documentData.author}</div>`
          : ""
      }
      ${
        documentData.description
          ? `<div class="description" style="font-size: 1.1em; line-height: 1.6; opacity: 0.8; max-width: 500px; margin: 0 auto;">${documentData.description}</div>`
          : ""
      }
      <div style="margin-top: 30px; font-size: 0.8em; opacity: 0.6;">Template rendering unavailable - using fallback design</div>
    </div>`;

    return fallbackHTML;
  }
};

/**
 * Generate enhanced cover HTML for export with consistent styling
 * Uses the same cover preview service to ensure consistency between preview and export
 * Supports both template-based and custom cover images
 * @param {Object} template - Template configuration (can be null for custom covers)
 * @param {Object} documentData - Document metadata
 * @returns {Promise<string>} HTML string with enhanced cover
 */
const generateEnhancedCoverHTML = async (template, documentData) => {
  try {
    // Validate that we have userId for logo data retrieval
    if (!documentData?.userId) {
      prodLogger.warn(
        "⚠️ No userId provided in documentData for export - logos may not display",
        {
          documentData,
          templateId: template?.id,
        }
      );
    } else {
      prodLogger.debug("✅ userId found in documentData for export", {
        userId: documentData.userId,
        templateId: template?.id,
      });
    }

    // Check if custom cover image should be used
    const customCoverConfig = documentData?.contentDetails?.customCoverImage;
    const useCustomCover =
      customCoverConfig?.enabled && customCoverConfig?.imageUrl;

    prodLogger.debug("🎨 Generating enhanced cover for export", {
      templateId: template?.id,
      templateName: template?.name,
      documentTitle: documentData.title,
      useCustomCover,
      customImageUrl: useCustomCover ? customCoverConfig.imageUrl : null,
      hasUserId: !!documentData.userId, // Important for logo loading
      userId: documentData.userId,
      localStorageKeys: Object.keys(localStorage).filter((k) =>
        k.includes("overlay")
      ),
      // Profile data availability for export monitoring
      hasProfileData: !!(
        documentData.full_name ||
        documentData.email ||
        documentData.organization
      ),
    });
    let coverPreview;

    if (useCustomCover) {
      // Generate custom cover preview
      coverPreview = await coverPreviewService.generateCustomCoverPreview(
        customCoverConfig,
        documentData,
        {
          quality: 0.95, // Higher quality for export
          format: "png",
        }
      );
    } else {
      // Use template-based cover with potential customizations
      const textOverlayCustomizations = getTextOverlayCustomizations(
        template?.id,
        documentData
      );
      const logoOverlayCustomizations = getLogoOverlayCustomizations(
        template?.id,
        documentData
      );
      const hasTextCustomizations =
        Object.keys(textOverlayCustomizations).length > 0;
      const hasLogoCustomizations =
        Object.keys(logoOverlayCustomizations).length > 0;
      const hasAnyCustomizations =
        hasTextCustomizations || hasLogoCustomizations;

      if (hasAnyCustomizations) {
        // Merge text and logo customizations
        const mergedCustomizations = {
          ...textOverlayCustomizations,
          ...logoOverlayCustomizations,
        };

        // CRITICAL: Ensure userId is included in customizations for logo loading
        if (documentData.userId && documentData.userId !== "undefined") {
          mergedCustomizations.userId = documentData.userId;
          prodLogger.debug("✅ Added userId to merged customizations", {
            userId: documentData.userId,
          });
        }

        prodLogger.debug("🎨 Applying overlay customizations to export cover", {
          templateId: template.id,
          textCustomizationCount: Object.keys(textOverlayCustomizations).length,
          logoCustomizationCount: Object.keys(logoOverlayCustomizations).length,
          hasUserId: !!mergedCustomizations.userId,
          userId: mergedCustomizations.userId,
          textCustomizations: textOverlayCustomizations,
          logoCustomizations: logoOverlayCustomizations,
          mergedCustomizations,
        });

        // Generate cover with customizations
        prodLogger.debug("Export: Generating cover with customizations", {
          templateId: template.id,
          hasProfileData: !!(documentData.full_name || documentData.email),
        });

        coverPreview =
          await coverPreviewService.generateCoverPreviewWithCustomizations(
            template,
            documentData,
            mergedCustomizations,
            {
              quality: 0.95, // Higher quality for export
              format: "png",
            }
          );
      } else {
        // Generate standard cover without customizations
        prodLogger.debug("Export: Generating standard cover", {
          templateId: template.id,
          hasProfileData: !!(documentData.full_name || documentData.email),
        });

        coverPreview = await coverPreviewService.generateCoverPreview(
          template,
          documentData,
          {
            quality: 0.95, // Higher quality for export
            format: "png",
          }
        );
      }
    }

    prodLogger.debug("✅ Enhanced cover generated successfully for export");

    // FULL-BLEED: Return the cover HTML with full-page styling
    return `<div class="enhanced-template-cover" style="
      page-break-after: always;
      width: 100%;
      height: 100vh;
      margin: 0;
      padding: 0;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    ">
      <img src="${coverPreview.coverImageData}" alt="Document Cover" style="
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        margin: 0;
        padding: 0;
      " />
    </div>`;
  } catch (error) {
    prodLogger.error("❌ Error generating enhanced cover for export:", error);

    // Fallback strategy
    const customCoverConfig = documentData?.contentDetails?.customCoverImage;
    if (customCoverConfig?.enabled && customCoverConfig?.fallbackTemplate) {
      prodLogger.debug("🔄 Attempting fallback to template-based cover");
      // Could fetch fallback template here, but for now use simple fallback
    }

    // Fallback to original template generation if available
    if (template) {
      return await generateTemplateImageHTML(template, documentData);
    }

    // Final fallback - simple text-based cover
    return generateSimpleCoverHTML(documentData);
  }
};

/**
 * Generate simple text-based cover HTML as final fallback
 * @param {Object} documentData - Document metadata
 * @returns {string} Simple cover HTML
 */
const generateSimpleCoverHTML = (documentData) => {
  const title = documentData?.title || "Untitled Document";
  const author = documentData?.author || "";
  const description = documentData?.description || "";

  return `<div class="simple-cover" style="
    page-break-after: always;
    width: 100%;
    height: 100vh;
    margin: 0;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: white;
    color: #333;
    box-sizing: border-box;
    overflow: hidden;
  ">
    <h1 style="font-size: 3rem; margin-bottom: 1rem; font-weight: bold; color: #333;">${title}</h1>
    ${
      author
        ? `<div style="font-size: 1.5rem; margin-bottom: 2rem; color: #666;">by ${author}</div>`
        : ""
    }
    ${
      description
        ? `<div style="font-size: 1.2rem; max-width: 600px; line-height: 1.6; color: #666;">${description}</div>`
        : ""
    }
  </div>`;
};

// Generate HTML content from document data with optional template support
const generateHtmlContent = async (
  documentData,
  generatedContent,
  options = {}
) => {
  const { title, author, description } = documentData;
  const { selectedTemplate = null } = options;

  // CRITICAL: Prioritize editorHTML (contains user-inserted images) over original chapters
  const useEditorHTML =
    generatedContent.editorHTML && generatedContent.editorHTML.trim();
  const chapters = generatedContent.chapters || [];

  // Generate title page content (async operation) - supports both templates and custom covers
  const customCoverConfig = documentData?.contentDetails?.customCoverImage;
  const hasCustomCover =
    customCoverConfig?.enabled && customCoverConfig?.imageUrl;

  let titlePageContent;
  if (hasCustomCover || selectedTemplate) {
    // Use enhanced cover generation for both custom and template covers
    titlePageContent = await generateEnhancedCoverHTML(
      selectedTemplate,
      documentData
    );
  } else {
    // Use styled simple cover for skip template exports
    prodLogger.debug(
      "⚡ Using simple cover for template-less export (Skip Template mode)"
    );
    titlePageContent = generateSimpleCoverHTML(documentData);
  }

  // FIXED: Ensure title page content is never empty to prevent blank page rendering
  if (!titlePageContent || titlePageContent.trim() === "") {
    prodLogger.warn("⚠️ Empty title page content detected, using fallback");
    titlePageContent = `<h1 class="title">${title || "Untitled Document"}</h1>`;
  }

  let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title || "Document"}</title>
    <meta name="description" content="${
      description || "Generated by RapidDoc AI"
    }">
    <meta name="author" content="${author || "RapidDoc AI"}">
    <style>
        /* Screen view styling - centered with max-width for readability */
        body {
            font-family: 'Georgia', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background: white;
        }

        /* FULL-BLEED: Ensure no backgrounds show through on cover pages */
        html {
            background: white;
            margin: 0;
            padding: 0;
        }
        .title-page {
            text-align: center;
            page-break-after: always;
            width: 100%;
            height: 100vh;
            margin: 0;
            padding: 0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
        }
        .author {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        .description {
            font-style: italic;
            color: #95a5a6;
            max-width: 600px;
            margin: 0 auto;
        }
        .chapter {
            margin-bottom: 40px;
            /* MODERN CSS: Use break-* properties instead of deprecated page-break-* */
            break-before: auto; /* Allow intelligent page breaks instead of forcing */
            break-inside: avoid; /* Keep chapter content together when possible */
            /* FALLBACK: Legacy browser support */
            page-break-before: auto;
            page-break-inside: avoid;
        }
        .chapter-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
            padding-bottom: 10px;
            letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
            /* MODERN CSS: Use break-* properties */
            break-after: avoid; /* Keep title with following content */
            break-inside: avoid; /* Prevent title from being split */
            /* FALLBACK: Legacy browser support */
            page-break-after: avoid;
            page-break-inside: avoid;
        }
        .chapter-content {
            text-align: justify;
        }
        .chapter-image {
            margin: 20px 0;
            text-align: center;
            page-break-inside: avoid;
        }
        .chapter-image img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .image-credit {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }
        h1, h2, h3 {
            color: #000000;
            margin-top: 30px;
            margin-bottom: 15px;
            letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
            /* MODERN CSS: Use break-* properties */
            break-after: avoid; /* Keep headings with following content */
            break-inside: avoid; /* Prevent headings from being split */
            /* FALLBACK: Legacy browser support */
            page-break-after: avoid;
            page-break-inside: avoid;
        }
        p {
            margin-bottom: 15px;
            orphans: 3; /* FIXED: Prevent orphaned lines at bottom of page */
            widows: 3; /* FIXED: Prevent widowed lines at top of page */
        }
        @media print {
            /* FIXED: Consistent page setup for all pages to prevent margin inconsistencies */
            @page {
                size: A4;
                margin: 0.75in; /* Consistent margins for all pages - prevents orphaned heading margin issues */
                /* Remove browser-generated headers and footers */
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }

            /* FIXED: Remove conflicting margin/padding that causes blank pages */
            html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0 !important;
                padding: 0 !important;
                height: auto !important;
                background: white !important;
            }

            body {
                font-size: 14pt; /* Increased from 12pt for better readability */
                line-height: 1.5;
                max-width: none !important; /* Override screen max-width for full page usage */
                margin: 0 !important; /* Override screen centering for PDF */
                padding: 0 !important; /* Remove padding - margins handled by @page rule */

                /* Ensure proper text rendering with balanced margins */
                text-align: left; /* Maintain left alignment for readability */
                word-wrap: break-word; /* Handle long words gracefully */
                overflow-wrap: break-word; /* Modern word wrapping */
            }

            /* FULL-BLEED: Cover page takes full page without margins */
            .title-page {
                margin: 0 !important;
                padding: 0 !important;
                page-break-after: always;
                width: 100vw !important;
                height: 100vh !important;
                background: white !important;
            }

            /* FULL-BLEED: Cover images fill entire page */
            .template-cover,
            .enhanced-template-cover {
                margin: 0 !important;
                padding: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: white !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .template-cover img,
            .enhanced-template-cover img {
                width: 100vw !important;
                height: 100vh !important;
                object-fit: cover !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* FIXED: Content pages use consistent margins - no additional centering */
            .chapter-content {
                margin: 0; /* No additional margins - page margins handle spacing */
                width: 100%; /* Use full available width within page margins */
                padding: 0; /* No additional padding */
            }

            .chapter {
                /* MODERN CSS: Use break-* properties instead of deprecated page-break-* */
                break-before: auto; /* Allow intelligent page breaks instead of forcing */
                break-inside: avoid; /* Keep chapter content together when possible */
                /* FALLBACK: Legacy browser support */
                page-break-before: auto;
                page-break-inside: avoid;
                width: 100%; /* Use full available width within page margins */
                margin: 0; /* No additional margins - page margins handle spacing */
                padding: 0; /* No additional padding */
            }

            /* Content elements use full available width with balanced spacing */
            p, h1, h2, h3, h4, h5, h6, ul, ol, blockquote {
                width: 100%;
                box-sizing: border-box;
                margin-left: 0;
                margin-right: 0;
                orphans: 3; /* FIXED: Prevent orphaned lines at bottom of page */
                widows: 3; /* FIXED: Prevent widowed lines at top of page */
            }

            /* FIXED: Enhanced heading pagination rules for better content flow */
            h1, h2, h3, h4, h5, h6 {
                /* MODERN CSS: Use break-* properties */
                break-after: avoid; /* Keep headings with following content */
                break-inside: avoid; /* Prevent headings from being split */
                /* FALLBACK: Legacy browser support */
                page-break-after: avoid;
                page-break-inside: avoid;
            }

            /* FIXED: Keep chapter titles with their content */
            .chapter-title {
                /* MODERN CSS: Use break-* properties */
                break-after: avoid; /* Keep title with following content */
                break-inside: avoid; /* Prevent title from being split */
                /* FALLBACK: Legacy browser support */
                page-break-after: avoid;
                page-break-inside: avoid;
            }

            /* Tables and images use full available width */
            table, img, pre, code {
                width: 100%;
                box-sizing: border-box;
                max-width: 100%;
            }

            /* Enhanced image handling with balanced spacing */
            .chapter-image, img {
                page-break-inside: avoid;
                max-width: 100%;
                height: auto;
                margin: 0.5rem 0; /* Consistent top/bottom spacing, no left/right margins */
                display: block;
            }

            /* Improve table presentation with balanced layout */
            table {
                margin: 0.5rem 0; /* Consistent top/bottom spacing, no left/right margins */
                border-collapse: collapse;
                font-size: 0.9em;
            }

            /* Code blocks with better formatting for narrow content */
            pre, code {
                font-size: 0.85em;
                overflow-wrap: break-word;
                word-wrap: break-word;
            }
        }
    </style>
</head>
<body>
    <div class="title-page">
        ${titlePageContent}
    </div>
`;

  // CRITICAL: Use editor HTML content if available (contains user-inserted images)
  if (useEditorHTML) {
    // Clean up editor HTML for export
    const cleanedEditorContent = cleanEditorHTMLForExport(
      generatedContent.editorHTML
    );

    // FIXED: Parse and restructure content into proper chapters for better pagination
    const structuredContent =
      parseEditorContentIntoChapters(cleanedEditorContent);
    htmlContent += structuredContent;
  } else {
    prodLogger.debug(
      "📄 Export: Using original chapter content (fallback - no user edits)"
    );
    // Fallback: Use original chapters (legacy content without user edits)
    chapters.forEach((chapter, index) => {
      const chapterNumber = chapter.number || index + 1;
      const chapterTitle = chapter.title || "Untitled Chapter";
      const chapterContent = chapter.content || "";

      // Check if chapter content already contains a chapter heading
      const hasHeading = hasExistingChapterHeading(
        chapterContent,
        chapterNumber,
        chapterTitle
      );

      htmlContent += `<div class="chapter">`;

      // Only add programmatic chapter heading if not already present in content
      if (!hasHeading) {
        htmlContent += `<h2 class="chapter-title">${chapterTitle}</h2>`;
      }

      htmlContent += `
          <div class="chapter-content">
              ${markdownToHtml(chapterContent)}
          </div>
      </div>
  `;
    });
  }

  // Add JavaScript pagination fix as a robust fallback
  htmlContent += addJavaScriptPaginationFix();

  htmlContent += `
</body>
</html>`;

  return htmlContent;
};

// Create and download file
const downloadFile = (content, filename, mimeType) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Export as HTML
export const exportAsHtml = async (
  documentData,
  generatedContent,
  options = {}
) => {
  try {
    const { selectedTemplate = null } = options;
    const htmlContent = await generateHtmlContent(
      documentData,
      generatedContent,
      { selectedTemplate }
    );
    const filename = `${documentData.title || "document"}.html`;
    downloadFile(htmlContent, filename, "text/html");
    return { success: true, message: "HTML export completed successfully" };
  } catch (error) {
    prodLogger.error("HTML export failed:", error);
    return { success: false, error: error.message };
  }
};

// Export as PDF (using browser's print functionality)
export const exportAsPdf = async (
  documentData,
  generatedContent,
  options = {}
) => {
  try {
    const { selectedTemplate = null } = options;

    // Track template usage if template is selected
    if (selectedTemplate) {
      try {
        await incrementTemplateUsage(selectedTemplate.id);
        prodLogger.debug(
          `📊 Template usage incremented for: ${selectedTemplate.name}`
        );
      } catch (error) {
        prodLogger.warn("Failed to increment template usage:", error);
      }
    }

    // Create a new window with the HTML content
    const htmlContent = await generateHtmlContent(
      documentData,
      generatedContent,
      { selectedTemplate }
    );

    // Create a blob URL to avoid "about:blank" in the address bar
    const blob = new Blob([htmlContent], { type: "text/html" });
    const blobUrl = URL.createObjectURL(blob);

    // Open the blob URL instead of about:blank
    const printWindow = window.open(blobUrl, "_blank");

    // Fallback: if blob URL doesn't work, use the traditional method with improved title
    if (!printWindow || printWindow.closed) {
      const fallbackWindow = window.open("", "_blank");
      fallbackWindow.document.write(htmlContent);
      fallbackWindow.document.close();

      // Set a proper document title to replace "about:blank"
      fallbackWindow.document.title = documentData.title || "Document";

      // FIXED: Wait for content to load with proper timing
      fallbackWindow.onload = () => {
        setTimeout(() => {
          if (fallbackWindow.document.readyState === "complete") {
            fallbackWindow.print();
            fallbackWindow.close();
            // Clean up blob URL
            URL.revokeObjectURL(blobUrl);
          } else {
            setTimeout(() => {
              fallbackWindow.print();
              fallbackWindow.close();
              URL.revokeObjectURL(blobUrl);
            }, 500);
          }
        }, 1000); // Increased delay
      };
    } else {
      // FIXED: Wait for content to load with longer delay to prevent blank page issues
      printWindow.onload = () => {
        // Ensure all images and content are fully loaded
        setTimeout(() => {
          // Additional check to ensure document is ready
          if (printWindow.document.readyState === "complete") {
            printWindow.print();
            printWindow.close();
            // Clean up blob URL
            URL.revokeObjectURL(blobUrl);
          } else {
            // Wait a bit more if document isn't ready
            setTimeout(() => {
              printWindow.print();
              printWindow.close();
              URL.revokeObjectURL(blobUrl);
            }, 500);
          }
        }, 1000); // Increased delay from 500ms to 1000ms
      };
    }

    return {
      success: true,
      message:
        "PDF export initiated. Please use your browser's print dialog to save as PDF.",
    };
  } catch (error) {
    prodLogger.error("PDF export failed:", error);
    return { success: false, error: error.message };
  }
};

// Export as DOCX with embedded images using proper DOCX generation
export const exportAsDocx = async (
  documentData,
  generatedContent,
  editorInstance = null,
  options = {}
) => {
  try {
    // Import the DOCX generation service
    const { generateDocxWithImages, downloadDocxFile } = await import(
      "./docxGenerationService.js"
    );

    // CRITICAL: Extract content from the document, prioritizing editorHTML
    let content = "";
    let contentType = "markdown"; // default
    let contentSource = "chapters"; // Track content source to prevent duplicate headings

    // Priority 1: Editor instance (live editing session)
    if (editorInstance && typeof editorInstance.getHTML === "function") {
      prodLogger.debug("📄 DOCX Export: Using live editor instance content");
      const rawEditorContent = editorInstance.getHTML();
      content = cleanEditorHTMLForExport(rawEditorContent);
      contentType = "html";
      contentSource = "editor";
    }
    // Priority 2: Saved editor HTML (contains user-inserted images)
    else if (
      generatedContent.editorHTML &&
      generatedContent.editorHTML.trim()
    ) {
      prodLogger.debug(
        "📄 DOCX Export: Using saved editor HTML content (includes user-inserted images)"
      );
      const rawEditorHTML = generatedContent.editorHTML;
      content = cleanEditorHTMLForExport(rawEditorHTML);
      contentType = "html";
      contentSource = "editor";
    }
    // Priority 3: Fallback to original chapters (no user edits)
    else {
      prodLogger.debug(
        "📄 DOCX Export: Using original chapter content (fallback - no user edits)"
      );
      const chapters = generatedContent.chapters || [];

      // Import the hasExistingChapterHeading function
      const { hasExistingChapterHeading } = await import(
        "../utils/contentProcessing.js"
      );

      content = chapters
        .map((chapter, index) => {
          const chapterNumber = chapter.number || index + 1;
          const chapterTitle = chapter.title || "Untitled Chapter";
          const chapterContent = chapter.content || "";

          // Check if chapter content already contains a chapter heading to prevent duplicates
          const hasHeading = hasExistingChapterHeading(
            chapterContent,
            chapterNumber,
            chapterTitle
          );

          if (hasHeading) {
            // Content already has heading, use it directly
            return chapterContent;
          } else {
            // Content doesn't have heading, add it
            const programmaticTitle = `# ${chapterTitle}`;
            return `${programmaticTitle}\n\n${chapterContent}`;
          }
        })
        .join("\n\n---\n\n");
      contentType = "markdown";
      contentSource = "chapters";
    }

    // Generate DOCX with embedded images
    prodLogger.debug("📄 DOCX Export: Starting generation...");

    const result = await generateDocxWithImages(
      documentData,
      content,
      contentType,
      {
        // Pass selected template for cover generation
        selectedTemplate: options.selectedTemplate,
        // Pass content source for potential future use
        contentSource: contentSource,
        // Optimized settings for faster export
        imageProcessing: {
          concurrency: 2, // Reduce concurrent downloads to avoid bottlenecks
          maxRetries: 2, // Reduce retries for faster failure handling
          timeout: 8000, // Reduce timeout per image to 8 seconds
          skipOnError: true, // Skip problematic images rather than failing
          logProgress: false, // Disable verbose logging for performance
        },
      }
    );

    if (result.success) {
      // Download the generated DOCX file
      const filename = `${documentData.title || "document"}.docx`;
      downloadDocxFile(result.blob, filename);

      // Create detailed success message with image statistics
      let message = "DOCX export completed successfully with embedded images";
      if (result.imageStats && result.imageStats.totalImages > 0) {
        const { totalImages, successfulImages, failedImages } =
          result.imageStats;
        message += `. Images: ${successfulImages}/${totalImages} embedded successfully`;
        if (failedImages > 0) {
          message += `, ${failedImages} failed to download`;
        }
      }

      return {
        success: true,
        message: message,
        imageStats: result.imageStats,
        userMessage: result.userMessage || message,
      };
    } else {
      return {
        success: false,
        error: result.error || "DOCX generation failed",
        userMessage: result.userMessage || "Failed to generate DOCX file",
      };
    }
  } catch (error) {
    prodLogger.error("DOCX export failed:", error);
    return {
      success: false,
      error: error.message,
      userMessage:
        "An unexpected error occurred during DOCX export. Please try again.",
    };
  }
};

// Main export function
export const exportDocument = async (
  format,
  documentData,
  generatedContent,
  options = {}
) => {
  if (!documentData || !generatedContent) {
    return {
      success: false,
      error: "Missing document data or content",
      userMessage: "Unable to export: missing document data or content",
    };
  }

  try {
    const { editorInstance } = options;

    switch (format.toLowerCase()) {
      case "pdf":
        return await exportAsPdf(documentData, generatedContent, options);
      case "html":
        return await exportAsHtml(documentData, generatedContent, options);
      case "docx":
      case "word":
        return await exportAsDocx(
          documentData,
          generatedContent,
          editorInstance,
          options
        );
      default:
        return {
          success: false,
          error: `Unsupported export format: ${format}`,
          userMessage: `Export format "${format}" is not supported`,
        };
    }
  } catch (error) {
    prodLogger.error("Export failed:", error);
    return {
      success: false,
      error: error.message,
      userMessage: "An error occurred during export. Please try again.",
    };
  }
};

// Import the new comprehensive statistics service
import { getEnhancedDocumentStatistics } from "./documentStatisticsService.js";

// Get document statistics - Enhanced version
export const getDocumentStatistics = (
  generatedContent,
  editorContent = null
) => {
  try {
    // Use the enhanced statistics service for comprehensive calculation
    const enhancedStats = getEnhancedDocumentStatistics(
      generatedContent,
      editorContent
    );

    prodLogger.debug("📊 Document statistics calculated:", {
      source: enhancedStats.source,
      words: enhancedStats.words,
      chapters: enhancedStats.chapters,
      hasGeneratedContent: !!generatedContent,
      hasEditorContent: !!editorContent,
    });

    return {
      pages: enhancedStats.pages,
      chapters: enhancedStats.chapters,
      words: enhancedStats.words,
      readTime: enhancedStats.readTime,
      characters: enhancedStats.characters,
      sections: enhancedStats.sections,
    };
  } catch (error) {
    prodLogger.error("Error in getDocumentStatistics:", error);
    // Fallback to original logic for safety
    return getDocumentStatisticsLegacy(generatedContent);
  }
};

// Legacy statistics calculation (kept as fallback)
const getDocumentStatisticsLegacy = (generatedContent) => {
  if (!generatedContent || !generatedContent.chapters) {
    return { pages: 0, chapters: 0, words: 0, readTime: 0 };
  }

  const chapters = generatedContent.chapters.length;
  const words = generatedContent.chapters.reduce(
    (total, chapter) => total + (chapter.wordCount || 0),
    0
  );
  const pages = Math.ceil(words / 250); // Approximate 250 words per page
  const readTime = Math.ceil(words / 200); // Approximate 200 words per minute

  return { pages, chapters, words, readTime };
};

/**
 * Export template as standalone image (PNG/JPG)
 * @param {Object} template - Template configuration
 * @param {Object} documentData - Document metadata
 * @param {Object} options - Export options
 * @returns {Promise<Object>} Export result
 */
export const exportTemplateAsImage = async (
  template,
  documentData,
  options = {}
) => {
  try {
    const { format = "png", quality = 0.95, filename = null } = options;

    prodLogger.debug(`📸 Exporting template as ${format.toUpperCase()}:`, {
      templateName: template.name,
      templateId: template.id,
      format,
      options,
      localStorageKeys: Object.keys(localStorage).filter((k) =>
        k.includes("overlay")
      ),
    });

    // Validate template
    if (!template.background_image_url || !template.text_overlays) {
      throw new Error("Invalid template structure");
    }

    // Check for text and logo overlay customizations
    const textOverlayCustomizations = getTextOverlayCustomizations(
      template.id,
      documentData
    );
    const logoOverlayCustomizations = getLogoOverlayCustomizations(
      template.id,
      documentData
    );
    const hasTextCustomizations =
      Object.keys(textOverlayCustomizations).length > 0;
    const hasLogoCustomizations =
      Object.keys(logoOverlayCustomizations).length > 0;
    const hasAnyCustomizations = hasTextCustomizations || hasLogoCustomizations;

    let canvas;
    if (hasAnyCustomizations) {
      // Merge text and logo customizations
      const mergedCustomizations = {
        ...textOverlayCustomizations,
        ...logoOverlayCustomizations,
      };

      prodLogger.debug(
        "🎨 Applying overlay customizations to template image export",
        {
          templateId: template.id,
          textCustomizationCount: Object.keys(textOverlayCustomizations).length,
          logoCustomizationCount: Object.keys(logoOverlayCustomizations).length,
          textCustomizations: textOverlayCustomizations,
          logoCustomizations: logoOverlayCustomizations,
          mergedCustomizations,
        }
      );

      // Render template with customizations
      canvas = await imageOverlayService.renderTemplateWithCustomizations(
        template,
        documentData,
        mergedCustomizations
      );
    } else {
      // Render template without customizations
      canvas = await imageOverlayService.renderTemplate(
        template,
        documentData,
        {},
        {}
      );
    }

    const imageDataUrl = imageOverlayService.exportAsImage(
      canvas,
      format,
      quality
    );

    // Generate filename
    const exportFilename =
      filename || `${documentData.title || "document"}-cover.${format}`;

    // Download the image
    downloadFile(imageDataUrl, exportFilename, `image/${format}`);

    prodLogger.debug(
      `✅ Template exported successfully as ${format.toUpperCase()}`
    );

    return {
      success: true,
      message: `Template exported as ${format.toUpperCase()} successfully`,
      filename: exportFilename,
    };
  } catch (error) {
    prodLogger.error("Template image export failed:", error);
    return {
      success: false,
      error: error.message,
      userMessage: `Failed to export template as image: ${error.message}`,
    };
  }
};

/**
 * Export template with multiple format options
 * @param {Object} template - Template configuration
 * @param {Object} documentData - Document metadata
 * @param {Array} formats - Array of formats to export ['png', 'jpg', 'pdf']
 * @returns {Promise<Object>} Export results
 */
export const exportTemplateMultiFormat = async (
  template,
  documentData,
  formats = ["png"]
) => {
  try {
    prodLogger.debug(`📦 Exporting template in multiple formats:`, formats);

    const results = [];

    for (const format of formats) {
      try {
        if (format === "pdf") {
          // For PDF, use the existing PDF export with template
          const result = await exportAsPdf(
            documentData,
            { chapters: [] },
            { selectedTemplate: template }
          );
          results.push({
            format,
            success: result.success,
            error: result.error,
          });
        } else {
          // For image formats
          const result = await exportTemplateAsImage(template, documentData, {
            format,
            filename: `${documentData.title || "document"}-cover.${format}`,
          });
          results.push({
            format,
            success: result.success,
            error: result.error,
          });
        }
      } catch (error) {
        prodLogger.error(`Failed to export ${format}:`, error);
        results.push({ format, success: false, error: error.message });
      }
    }

    const successCount = results.filter((r) => r.success).length;
    const totalCount = results.length;

    prodLogger.debug(
      `✅ Multi-format export complete: ${successCount}/${totalCount} successful`
    );

    return {
      success: successCount > 0,
      results,
      message: `Exported ${successCount} of ${totalCount} formats successfully`,
    };
  } catch (error) {
    prodLogger.error("Multi-format export failed:", error);
    return {
      success: false,
      error: error.message,
      results: [],
    };
  }
};
