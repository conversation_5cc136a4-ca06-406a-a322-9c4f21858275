import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { projectsService } from '../../../../services/projectsService';
import Button from '../../../../components/ui/Button';

import { prodLogger } from '../../../../utils/prodLogger.js';
/**
 * ContentDetailsStep - Final content generation step with preview
 * Shows document outline and allows content generation with image options
 */
const ContentDetailsStep = ({
  formData,
  onInputChange,
  onValidationChange,
  onGenerate,
  onGenerateDocument,
  className = ''
}) => {
  prodLogger.debug('🏗️ ContentDetailsStep component rendered with props:', {
    onGenerateDocument: typeof onGenerateDocument,
    formData: !!formData,
    timestamp: new Date().toISOString()
  });

  const navigate = useNavigate();
  const [isGenerating, setIsGenerating] = useState(false);
  const formDataRef = useRef(formData);
  const handleGenerateRef = useRef(null);

  // Keep formDataRef current
  useEffect(() => {
    formDataRef.current = formData;
  }, [formData]);

  // Remove handleAddImages since it's now handled in ImageAdditionStep

  const handleGenerate = useCallback(async () => {
    setIsGenerating(true);
    prodLogger.debug('🚀 Starting document generation...');

    try {
      // Prepare project data for database
      const formDataCurrent = formDataRef.current;
      prodLogger.debug('📋 Form data:', formDataCurrent);

      // Extract title from the form data
      const projectTitle = formDataCurrent.titleSelection?.selectedTitle ||
                          formDataCurrent.topicAndNiche?.mainTopic ||
                          'Untitled Document';
      prodLogger.debug('📝 Project title:', projectTitle);

      // Map document type and category
      const documentType = formDataCurrent.documentPurpose?.primaryType || 'ebook';
      const subType = formDataCurrent.documentPurpose?.subType || null;
      prodLogger.debug('🔍 Raw document type from form data:', documentType, 'Subtype:', subType);

      const categoryMap = {
        'ebook': 'eBooks',
        'academic': 'Academic',
        'business': 'Business',
        'guide': 'eBooks'
      };
      const category = categoryMap[documentType] || 'eBooks';
      prodLogger.debug('🏷️ Document type:', documentType, 'Subtype:', subType, 'Category:', category);

      // Create project data
      const projectData = {
        title: projectTitle,
        description: `${subType ? `${subType} (${documentType})` : documentType} about ${formDataCurrent.topicAndNiche?.mainTopic || 'various topics'}`,
        document_type: documentType,
        document_subtype: subType,
        category: category,
        status: 'draft',
        progress: 0,
        questionnaire_data: formDataCurrent,
        // No generated_content yet - will be added by Document Editor
        generated_content: null
      };
      prodLogger.debug('💾 Project data to save:', projectData);

      // Save project to database
      prodLogger.debug('🔄 Calling projectsService.createProject...');
      const response = await projectsService.createProject(projectData);
      prodLogger.debug('📡 API Response:', response);

      if (response.success) {
        const projectId = response.data.id;
        prodLogger.debug('✅ Project created successfully! ID:', projectId);

        // Store minimal data in localStorage for Document Editor compatibility
        const documentData = {
          ...formDataCurrent,
          documentId: projectId, // Use project ID as document ID
          projectId: projectId,
          createdAt: new Date().toISOString()
        };
        localStorage.setItem(`document-${projectId}`, JSON.stringify(documentData));
        prodLogger.debug('💾 Stored in localStorage for editor');

        // Navigate to document editor with project ID
        prodLogger.debug('🧭 Navigating to document editor...');
        navigate(`/document-editor/${projectId}`);
      } else {
        prodLogger.error('❌ API Error:', response.error);
        throw new Error(response.error?.message || 'Failed to create project');
      }

    } catch (error) {
      prodLogger.error('💥 Error creating document:', error);
      alert('Failed to create document. Please try again.');
      setIsGenerating(false);
    }
  }, [navigate]);



  // Validation - always valid for this step
  // Only call validation once on mount, not on every onValidationChange update
  useEffect(() => {
    onValidationChange?.(true);
  }, []); // Empty dependency array - only run on mount

  // Expose handleGenerate function to parent - only when component mounts
  useEffect(() => {
    prodLogger.debug('📤 ContentDetailsStep: Exposing handleGenerate to parent', {
      onGenerateDocument: typeof onGenerateDocument,
      handleGenerate: typeof handleGenerate
    });

    if (onGenerateDocument) {
      // Pass the handleGenerate function without the isGenerating state
      // The parent component shouldn't need to re-render when isGenerating changes
      onGenerateDocument(handleGenerate, false);
      prodLogger.debug('✅ Successfully passed handleGenerate to parent');
    } else {
      prodLogger.warn('⚠️ onGenerateDocument prop is not provided');
    }
  }, []); // Empty dependency array - only run on mount

  // Update parent when handleGenerate changes
  useEffect(() => {
    if (onGenerateDocument) {
      prodLogger.debug('🔄 Updating handleGenerate in parent due to function change');
      onGenerateDocument(handleGenerate, false);
    }
  }, [handleGenerate, onGenerateDocument]);

  return (
    <div className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Content Details
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          Review your document setup and generate your content
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Topic Card */}
        <div className="bg-white p-4 rounded-lg border border-border shadow-sm">
          <h3 className="font-semibold text-text-primary mb-2">Topic & Niche</h3>
          <p className="text-sm text-text-secondary mb-1">
            <strong>Main Topic:</strong> {formData.topicAndNiche?.mainTopic || 'Not specified'}
          </p>
          <p className="text-sm text-text-secondary mb-1">
            <strong>Language:</strong> {formData.topicAndNiche?.language || 'English'}
          </p>
          {(formData.topicAndNiche?.subNiches?.length > 0 || formData.topicAndNiche?.customSubNiche) && (
            <p className="text-sm text-text-secondary">
              <strong>Sub-niches:</strong> {
                (() => {
                  const allSubNiches = [
                    ...(formData.topicAndNiche.subNiches || []),
                    ...(formData.topicAndNiche.customSubNiche ? [formData.topicAndNiche.customSubNiche] : [])
                  ];
                  return allSubNiches.length > 2 
                    ? `${allSubNiches.slice(0, 2).join(', ')} +${allSubNiches.length - 2} more`
                    : allSubNiches.join(', ');
                })()
              }
            </p>
          )}
        </div>

        {/* Audience Card */}
        <div className="bg-white p-4 rounded-lg border border-border shadow-sm">
          <h3 className="font-semibold text-text-primary mb-2">Target Audience</h3>
          <p className="text-sm text-text-secondary">
            {(() => {
              const isAcademic = formData.documentPurpose?.primaryType === 'academic';
              const audience = formData.audienceAnalysis?.primaryAudience || 'Not specified';
              
              if (isAcademic && ['undergraduate', 'masters', 'phd'].includes(audience)) {
                // Show formatted academic level
                const levelNames = {
                  undergraduate: 'Undergraduate',
                  masters: 'Masters',
                  phd: 'PhD'
                };
                return `${levelNames[audience]} level academic readership`;
              }
              
              return audience;
            })()
          }
          </p>
          {formData.audienceAnalysis?.context && (
            <p className="text-xs text-text-secondary mt-2 line-clamp-3">
              {formData.audienceAnalysis.context}
            </p>
          )}
        </div>

        {/* Style Card */}
        <div className="bg-white p-4 rounded-lg border border-border shadow-sm">
          <h3 className="font-semibold text-text-primary mb-2">Style & Tone</h3>
          <p className="text-sm text-text-secondary mb-1">
            <strong>Tone:</strong> {formData.toneAndVoice?.toneOfVoice || 'Informative'}
          </p>
          <p className="text-sm text-text-secondary mb-1">
            <strong>Formality:</strong> {formData.toneAndVoice?.formalityLevel || 'Semi-formal'}
          </p>
          <p className="text-sm text-text-secondary">
            <strong>Focus Area:</strong> {
              formData.topicAndNiche?.subNiches?.length > 0
                ? formData.topicAndNiche.subNiches[0].name || formData.topicAndNiche.subNiches[0]
                : formData.topicAndNiche?.customSubNiche || 'General Topic'
            }
          </p>
        </div>
      </div>

      {/* Document Title */}
      {formData.titleSelection?.selectedTitle && (
        <div className="bg-primary/5 p-6 rounded-lg border border-primary/20">
          <h3 className="font-semibold text-text-primary mb-2">Selected Title</h3>
          <p className="text-lg font-medium text-text-primary">
            {formData.titleSelection.selectedTitle}
          </p>
        </div>
      )}

      {/* Approved Document Outline */}
      {formData.documentOutline?.generatedOutline && (
        <div className="bg-white p-6 rounded-lg border border-border shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-text-primary">Approved Document Outline</h3>
            <div className="flex items-center space-x-2 text-green-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">Approved</span>
            </div>
          </div>

          <div className="space-y-4">
            {formData.documentOutline.generatedOutline.chapters?.map((chapter) => (
              <div key={chapter.number} className="border-l-4 border-green-400 pl-4">
                <h4 className="font-medium text-text-primary mb-2">
                  {chapter.title}
                </h4>
                <ul className="space-y-1">
                  {chapter.sections?.map((section, index) => (
                    <li key={index} className="text-sm text-text-secondary flex items-start">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {section}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Image Status Display - Show if images were configured in outline step */}
      {formData.imageAddition?.enabled && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center space-x-4">
            <div className="bg-green-100 p-3 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h3 className="font-semibold text-green-800">Images Ready</h3>
              <p className="text-sm text-green-700">
                AI-generated image suggestions are ready and will be available in the document editor for selection and placement.
              </p>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default ContentDetailsStep;
