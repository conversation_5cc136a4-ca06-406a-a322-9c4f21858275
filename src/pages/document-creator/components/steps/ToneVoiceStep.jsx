import React, { useEffect } from 'react';
import CustomDropdown from '../CustomDropdown';
import {
  getToneOptionsForDocumentType
} from '../../utils/questionnaireDataStructure';
import { getDocumentTerminology } from '../../constants/documentOptions';

/**
 * ToneVoiceStep - Tone and voice selection for document creation
 * Focuses on tone, formality, and perspective settings
 */
const ToneVoiceStep = ({
  formData,
  onInputChange,
  onValidationChange,
  className = ''
}) => {
  // Get dynamic terminology based on document type and subtype
  const documentType = formData.documentPurpose?.primaryType || 'ebook';
  const subType = formData.documentPurpose?.subType || null;
  const terminology = getDocumentTerminology(documentType, subType);

  // Get document type-specific tone options (with subtype support)
  const toneOptions = getToneOptionsForDocumentType(documentType, subType).map(option => ({
    id: option.id,
    name: option.name,
    description: option.description
  }));

  const handleToneChange = (toneId) => {
    onInputChange('toneAndVoice.toneOfVoice', toneId);

    // Set default writing style based on tone and document type
    const toneOption = toneOptions.find(t => t.id === toneId);
    if (toneOption) {
      // Set intelligent defaults based on document type and tone
      if (documentType === 'academic') {
        onInputChange('toneAndVoice.writingStyle', 'academic');
        onInputChange('toneAndVoice.formalityLevel', 'formal');
      } else if (documentType === 'business') {
        onInputChange('toneAndVoice.writingStyle', 'professional');
        onInputChange('toneAndVoice.formalityLevel', 'formal');
      } else {
        // eBook defaults
        if (toneId === 'conversational' || toneId === 'empathetic') {
          onInputChange('toneAndVoice.writingStyle', 'conversational');
          onInputChange('toneAndVoice.formalityLevel', 'casual');
        } else {
          onInputChange('toneAndVoice.writingStyle', 'professional');
          onInputChange('toneAndVoice.formalityLevel', 'semi-formal');
        }
      }
    }
  };

  // Validation - only require tone selection
  useEffect(() => {
    const isValid = formData.toneAndVoice?.toneOfVoice;
    onValidationChange?.(isValid);
  }, [formData.toneAndVoice]);

  return (
    <div className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Set Your Document's Tone & Voice
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          Choose the tone and writing style that best fits your document and audience.
        </p>
      </div>

      {/* Tone of Voice Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Tone of voice
        </label>
        <CustomDropdown
          value={formData.toneAndVoice?.toneOfVoice || 'informative'}
          onChange={handleToneChange}
          options={toneOptions}
          placeholder="Informative"
          className="w-full max-w-md"
        />
      </div>

      {/* Additional Voice Settings */}
      <div className="space-y-6 pt-6 border-t border-border">
        <h3 className="text-lg font-semibold text-text-primary">
          Additional Voice Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Formality Level */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-text-primary">
              Formality Level
            </label>
            <CustomDropdown
              value={formData.toneAndVoice?.formalityLevel || 'semi-formal'}
              onChange={(value) => onInputChange('toneAndVoice.formalityLevel', value)}
              options={[
                { id: 'very-formal', name: 'Very Formal' },
                { id: 'formal', name: 'Formal' },
                { id: 'semi-formal', name: 'Semi-formal' },
                { id: 'casual', name: 'Casual' },
                { id: 'very-casual', name: 'Very Casual' }
              ]}
              placeholder="Semi-formal"
              className="w-full"
            />
          </div>

          {/* Perspective */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-text-primary">
              Perspective
            </label>
            <CustomDropdown
              value={formData.toneAndVoice?.perspectiveVoice || 'third-person'}
              onChange={(value) => onInputChange('toneAndVoice.perspectiveVoice', value)}
              options={[
                { id: 'first-person', name: 'First Person (I, we)' },
                { id: 'second-person', name: 'Second Person (you)' },
                { id: 'third-person', name: 'Third Person (they, it)' },
                { id: 'mixed', name: 'Mixed' }
              ]}
              placeholder="Third Person"
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Suggestion Text */}
      <div className="text-center pt-4">
        <p className="text-sm text-text-secondary">
          Didn't find your perfect tone of voice?{' '}
          <button className="text-primary hover:text-primary/80 underline">
            Share your suggestions with us
          </button>
        </p>
      </div>
    </div>
  );
};

export default ToneVoiceStep;
